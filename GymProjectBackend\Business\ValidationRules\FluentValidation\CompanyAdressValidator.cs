﻿using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
   public class CompanyAdressValidator:AbstractValidator<CompanyAdress>
    {
        private readonly GymContext _context;

        // Constructor injection (Scalability için)
        public CompanyAdressValidator(GymContext context)
        {
            _context = context;

            RuleFor(c => c.Adress).NotEmpty().WithMessage("Adres boş bırakılamaz.");
            RuleFor(c => c.CompanyID).NotEmpty().WithMessage("Şirket ismi boş bırakılamaz.");
            RuleFor(c => c.CityID).NotEmpty().WithMessage("İl kısmı boş bırakılamaz.");
            RuleFor(c => c.TownID).NotEmpty().WithMessage("İlçe kısmı boş bırakılamaz.");
            RuleFor(c => c).Must(BeUniqueAdress).WithMessage("Bu şirkete zaten adres verilmiş.");

        }
        private bool BeUniqueAdress(CompanyAdress adress)
        {
            // DI kullanılıyor - Scalability optimized
            if (adress.CompanyAdressID != 0)
            {
                return !_context.CompanyAdresses.Any(c =>
                    c.Adress == adress.Adress &&
                    c.CompanyID == adress.CompanyID &&
                    c.CompanyAdressID != adress.CompanyAdressID &&
                    c.IsActive == true);
            }
            else
            {
                return !_context.CompanyAdresses.Any(c =>
                    c.Adress == adress.Adress &&
                    c.CompanyID == adress.CompanyID &&
                    c.IsActive == true);
            }
        }
    }
}
