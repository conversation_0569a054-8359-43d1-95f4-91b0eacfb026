import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  NativeDateAdapter,
  NativeDateModule,
  VERSION,
  provideNativeDateAdapter,
  setLines
} from "./chunk-UFRWRMZK.js";
import {
  ErrorStateMatcher,
  ShowOnDirtyErrorStateMatcher,
  _ErrorStateTracker
} from "./chunk-QIWW5JQN.js";
import {
  _MatInternalFormField
} from "./chunk-JB5YICTO.js";
import {
  MatRippleLoader
} from "./chunk-2WBQEI4T.js";
import {
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition
} from "./chunk-CR7IHQ42.js";
import {
  MatRippleModule
} from "./chunk-SU5TIWFW.js";
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatRipple,
  RippleRef,
  RippleRenderer,
  RippleState,
  defaultRippleAnimationConfig
} from "./chunk-DVNDVRTI.js";
import "./chunk-SKSD5LY4.js";
import {
  _StructuralStylesLoader
} from "./chunk-HC3Q3XIF.js";
import "./chunk-FTZZESUS.js";
import "./chunk-V2OSU5GV.js";
import "./chunk-ACEFEZTE.js";
import {
  MATERIAL_SANITY_CHECKS,
  MatCommonModule
} from "./chunk-UIHJ6YXX.js";
import "./chunk-ZFMR5WBM.js";
import "./chunk-V65MNCZN.js";
import "./chunk-HGNYHDJR.js";
import "./chunk-EXQLYBKH.js";
import "./chunk-HGVHWTGE.js";
import "./chunk-IUOK4BIQ.js";
import "./chunk-GBTWTWDP.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _StructuralStylesLoader,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  provideNativeDateAdapter,
  setLines
};
