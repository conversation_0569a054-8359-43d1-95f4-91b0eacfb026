{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\validationrules\\fluentvalidation\\companyadressvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\validationrules\\fluentvalidation\\companyadressvalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBB0198B-62B6-4641-B481-17B092B0C9C9}|Core\\Core.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\core\\dataaccess\\entityframework\\efentityrepositorybase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DBB0198B-62B6-4641-B481-17B092B0C9C9}|Core\\Core.csproj|solutionrelative:core\\dataaccess\\entityframework\\efentityrepositorybase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBB0198B-62B6-4641-B481-17B092B0C9C9}|Core\\Core.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\core\\dataaccess\\entityframework\\efcompanyentityrepositorybase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DBB0198B-62B6-4641-B481-17B092B0C9C9}|Core\\Core.csproj|solutionrelative:core\\dataaccess\\entityframework\\efcompanyentityrepositorybase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmemberdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmemberdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\membercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\membercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\membermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\membermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efremainingdebtdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efremainingdebtdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efsystemexercisedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efsystemexercisedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efunifiedcompanydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efunifiedcompanydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmembershipdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmembershipdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efcompanyadressdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efcompanyadressdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efcitydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efcitydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efworkoutprogramtemplatedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efworkoutprogramtemplatedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\workoutprogramtemplatemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\workoutprogramtemplatemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\package-lock.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\package-lock.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\properties\\publishprofiles\\folderprofile.pubxml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\properties\\publishprofiles\\folderprofile.pubxml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\qrcodeencryptionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\qrcodeencryptionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 149, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "CompanyAdressValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\CompanyAdressValidator.cs", "RelativeDocumentMoniker": "Business\\ValidationRules\\FluentValidation\\CompanyAdressValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\ValidationRules\\FluentValidation\\CompanyAdressValidator.cs", "RelativeToolTip": "Business\\ValidationRules\\FluentValidation\\CompanyAdressValidator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACgAAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T17:08:25.429Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "EfEntityRepositoryBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Core\\DataAccess\\EntityFramework\\EfEntityRepositoryBase.cs", "RelativeDocumentMoniker": "Core\\DataAccess\\EntityFramework\\EfEntityRepositoryBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Core\\DataAccess\\EntityFramework\\EfEntityRepositoryBase.cs", "RelativeToolTip": "Core\\DataAccess\\EntityFramework\\EfEntityRepositoryBase.cs", "ViewState": "AgIAABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T16:54:14.559Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "EfCompanyEntityRepositoryBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Core\\DataAccess\\EntityFramework\\EfCompanyEntityRepositoryBase.cs", "RelativeDocumentMoniker": "Core\\DataAccess\\EntityFramework\\EfCompanyEntityRepositoryBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Core\\DataAccess\\EntityFramework\\EfCompanyEntityRepositoryBase.cs", "RelativeToolTip": "Core\\DataAccess\\EntityFramework\\EfCompanyEntityRepositoryBase.cs", "ViewState": "AgIAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T16:51:59.834Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "EfMemberDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "ViewState": "AgIAAN4BAAAAAAAAAAAAAP0BAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:31:42.761Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "MemberManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MemberManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberManager.cs", "RelativeToolTip": "Business\\Concrete\\MemberManager.cs", "ViewState": "AgIAAMQAAAAAAAAAAAAkwOYAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:31:32.286Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MemberController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\MemberController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberController.cs", "RelativeToolTip": "WebAPI\\Controllers\\MemberController.cs", "ViewState": "AgIAAEoAAAAAAAAAAAAkwG0AAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T12:31:14.155Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "EfRemainingDebtDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfRemainingDebtDal.cs", "ViewState": "AgIAAGYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T22:46:23.127Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "EfSystemExerciseDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfSystemExerciseDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfSystemExerciseDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfSystemExerciseDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfSystemExerciseDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T22:46:22.348Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "EfUnifiedCompanyDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUnifiedCompanyDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfUnifiedCompanyDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUnifiedCompanyDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfUnifiedCompanyDal.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T22:46:13.666Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "WorkoutProgramTemplateManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\WorkoutProgramTemplateManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\WorkoutProgramTemplateManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\WorkoutProgramTemplateManager.cs", "RelativeToolTip": "Business\\Concrete\\WorkoutProgramTemplateManager.cs", "ViewState": "AgIAADIAAAAAAAAAAAArwEYAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:17:55.375Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "FolderProfile.pubxml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\PublishProfiles\\FolderProfile.pubxml", "RelativeDocumentMoniker": "WebAPI\\Properties\\PublishProfiles\\FolderProfile.pubxml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\PublishProfiles\\FolderProfile.pubxml", "RelativeToolTip": "WebAPI\\Properties\\PublishProfiles\\FolderProfile.pubxml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-07-05T22:21:54.369Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Program.cs", "RelativeDocumentMoniker": "WebAPI\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Program.cs", "RelativeToolTip": "WebAPI\\Program.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T22:21:39.746Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "package-lock.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\package-lock.json", "RelativeDocumentMoniker": "WebAPI\\package-lock.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\package-lock.json", "RelativeToolTip": "WebAPI\\package-lock.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T22:21:38.404Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.Development.json", "RelativeDocumentMoniker": "WebAPI\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.Development.json", "RelativeToolTip": "WebAPI\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T22:21:30.189Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "WebAPI\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\launchSettings.json", "RelativeToolTip": "WebAPI\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T22:21:26.96Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "EfMembershipDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMembershipDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMembershipDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMembershipDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMembershipDal.cs", "ViewState": "AgIAADwAAAAAAAAAAAArwFYAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T15:36:01.774Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "EfCompanyAdressDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyAdressDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfCompanyAdressDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyAdressDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfCompanyAdressDal.cs", "ViewState": "AgIAABwAAAAAAAAAAAAYwCgAAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T20:48:10.328Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "EfWorkoutProgramTemplateDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAAABICAAB/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:17:55.961Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "EfCityDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCityDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfCityDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCityDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfCityDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T20:48:07.421Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "QrCodeEncryptionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\QrCodeEncryptionManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\QrCodeEncryptionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\QrCodeEncryptionManager.cs", "RelativeToolTip": "Business\\Concrete\\QrCodeEncryptionManager.cs", "ViewState": "AgIAAN4AAAAAAAAAAAAAAO4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T17:33:22.708Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeDocumentMoniker": "WebAPI\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeToolTip": "WebAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T22:22:06.161Z"}]}]}]}