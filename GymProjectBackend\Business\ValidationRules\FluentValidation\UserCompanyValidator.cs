﻿using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class UserCompanyValidator : AbstractValidator<UserCompany>
    {
        private readonly GymContext _context;

        // Constructor injection (Scalability için)
        public UserCompanyValidator(GymContext context)
        {
            _context = context;

            RuleFor(p => p.UserID).NotEmpty().WithMessage("Kullanıcı seçimi boş bırakılamaz.");
            RuleFor(p => p.CompanyId).NotEmpty().WithMessage("Şirket seçimi boş bırakılamaz.");
            RuleFor(p => p).Must(BeUniqueCompanyName).WithMessage("Bu şirket zaten bir kullanıcıya bağlı!");
        }

        private bool BeUniqueCompanyName(UserCompany userCompany)
        {
            // DI kullanılıyor - Scalability optimized
            if (userCompany.UserCompanyID != 0)
            {
                // Güncelleme durumu
                return !_context.UserCompanies.Any(uc =>
                    uc.CompanyId == userCompany.CompanyId &&
                    uc.UserCompanyID != userCompany.UserCompanyID &&
                    uc.IsActive == true);
            }
            else
            {
                // Yeni ekleme durumu
                return !_context.UserCompanies.Any(uc =>
                    uc.CompanyId == userCompany.CompanyId &&
                    uc.IsActive == true);
            }
        }
    }
}