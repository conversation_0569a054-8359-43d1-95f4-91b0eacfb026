🚨 SCALABILITY ÇÖZÜM PLANI - TÜM PROMPT'LAR V2 🚨

Bu dosyada 7 prompt sıra sıra yer alıyor. Her prompt'u ayrı ayrı AI'ya gönder.

================================================================================
🚨 PROMPT 1: DEPENDENCY INJECTION LIFETIME DÜZELTMESİ 🚨

GÖREV: AutofacBusinessModule'deki SingleInstance() sorununu çöz

MEVCUT SORUN:
- Tüm DAL ve Manager sınıfları SingleInstance() olarak kayıtlı
- Bu 100,000 kullanıcıda MEMORY LEAK'e neden olacak
- Thread-safety sorunları yaratacak

YAPILACAKLAR:

🔧 ADIM 1: AutofacBusinessModule Analizi
- Business/DependencyResolvers/Autofac/AutofacBusinessModule.cs dosyasını aç
- Mevcut SingleInstance() kayıtlarını listele
- Hangi sınıfların SingleInstance() kalması gerektiğini belirle

🔧 ADIM 2: Lifetime Değişiklikleri
DEĞIŞTIRILECEKLER (.InstancePerLifetimeScope() yapılacak):
- Tüm Manager sınıfları (UserManager, MemberManager, vs.)
- Tüm DAL sınıfları (EfUserDal, EfMemberDal, vs.)

KALACAKLAR (.SingleInstance() olarak):
- ITokenHelper, JwtHelper
- ILogService, FileLoggerService, PerformanceLoggerService
- ICacheManager, MultiTenantCacheManager
- Stopwatch
- IHttpContextAccessor

🔧 ADIM 3: Değişiklikleri Uygula
- AutofacBusinessModule.cs dosyasında gerekli değişiklikleri yap
- Sadece stateless ve thread-safe sınıfları SingleInstance() bırak

🔧 ADIM 4: Build Test
- Projeyi build et
- Hata varsa analiz et ve düzelt
- Dependency injection circular dependency kontrolü yap

BEKLENEN SONUÇ:
✅ Build SUCCESS
✅ Memory leak riski %90 azalma
✅ Thread-safety sorunları çözülmüş

BAŞARI KRİTERİ:
- Build hatası yok
- Circular dependency yok
- Sadece stateless sınıflar SingleInstance()

Bu prompt tamamlandıktan sonra "✅ PROMPT 1 TAMAMLANDI - PROMPT 2'YE GEÇİLEBİLİR" yaz.

=== PROMPT 1 SONU ===

================================================================================
🚨 PROMPT 2: DbContext DEPENDENCY INJECTION KURULUMU 🚨

GÖREV: GymContext'i DI container'a kaydet ve base sınıfları hazırla

MEVCUT SORUN:
- Her metodda "using (var context = new GymContext())" kullanılıyor
- DbContext creation overhead'i çok yüksek
- Connection pool tükenme riski

YAPILACAKLAR:

🔧 ADIM 1: GymContext DI Kaydı
- WebAPI/Program.cs dosyasını aç
- builder.Services.AddDbContext<GymContext>() kaydını ekle
- Connection string'i environment'a göre ayarla

EKLENECEK KOD:
```csharp
// DbContext DI kaydı
var environment = builder.Configuration["Environment"] ?? "dev";
var connectionString = builder.Configuration[$"ConnectionStrings:{environment}"];

builder.Services.AddDbContext<GymContext>(options =>
    options.UseSqlServer(connectionString));
```

🔧 ADIM 2: GymContext Constructor Güncelleme
- DataAccess/Concrete/EntityFramework/GymContext.cs dosyasını aç
- DbContextOptions<GymContext> parametresi alan constructor ekle
- Mevcut OnConfiguring metodunu koşullu yap

GÜNCELLENECEK KOD:
```csharp
public class GymContext : DbContext
{
    public GymContext() { } // Parameterless constructor (backward compatibility)
    
    public GymContext(DbContextOptions<GymContext> options) : base(options) { } // DI constructor
    
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        // Sadece options set edilmemişse çalışsın
        if (!optionsBuilder.IsConfigured)
        {
            // Mevcut configuration logic
        }
    }
}
```

🔧 ADIM 3: Base Sınıfları Güncelle
A) Core/DataAccess/EntityFramework/EfEntityRepositoryBase.cs:
- Constructor'a TContext injection ekle
- "using (TContext context = new TContext())" pattern'lerini kaldır
- _context field kullan

B) Core/DataAccess/EntityFramework/EfCompanyEntityRepositoryBase.cs:
- Constructor'a TContext injection ekle
- Base class constructor'ını güncelle

🔧 ADIM 4: Build Test
- Projeyi build et
- DI registration doğru mu kontrol et
- DbContext injection çalışıyor mu test et

BEKLENEN SONUÇ:
✅ Build SUCCESS
✅ DbContext DI container'da kayıtlı
✅ Base sınıflar injection'a hazır

BAŞARI KRİTERİ:
- Build hatası yok
- DbContext DI kaydı aktif
- Base sınıflar güncellenmiş

Bu prompt tamamlandıktan sonra "✅ PROMPT 2 TAMAMLANDI - PROMPT 3'E GEÇİLEBİLİR" yaz.

=== PROMPT 2 SONU ===

================================================================================
🚨 PROMPT 3: DAL REFACTOR - BATCH 1 (İLK 10 DOSYA) 🚨

GÖREV: İlk 10 DAL dosyasını DbContext injection'a geçir

⚠️ Bu batch'te sadece 10 dosya işlenecek, fazlasına dokunma!

REFACTOR EDİLECEK DOSYALAR (BATCH 1):
1. EfCityDal.cs
2. EfCompanyAdressDal.cs  
3. EfCompanyDal.cs
4. EfCompanyExerciseDal.cs
5. EfCompanyUserDal.cs
6. EfDebtPaymentDal.cs
7. EfEntryExitHistoryDal.cs
8. EfExerciseCategoryDal.cs
9. EfExpenseDal.cs
10. EfLicensePackageDal.cs

🔧 HER DOSYA İÇİN YAPILACAKLAR:

A) Constructor'a GymContext injection ekle:
```csharp
private readonly GymContext _context;

public EfXXXDal(ICompanyContext companyContext, GymContext context) : base(companyContext)
{
    _companyContext = companyContext;
    _context = context;
}
```

B) Tüm "using (GymContext context = new GymContext())" pattern'lerini kaldır

C) _context kullanacak şekilde refactor et:
```csharp
// ÖNCE:
using (GymContext context = new GymContext())
{
    var result = context.Members.Where(...);
}

// SONRA:
var result = _context.Members.Where(...);
```

🔧 REFACTOR SIRASI:
1. EfCityDal.cs → Build test
2. EfCompanyAdressDal.cs → Build test  
3. EfCompanyDal.cs → Build test
4. EfCompanyExerciseDal.cs → Build test
5. EfCompanyUserDal.cs → Build test
6. EfDebtPaymentDal.cs → Build test
7. EfEntryExitHistoryDal.cs → Build test
8. EfExerciseCategoryDal.cs → Build test
9. EfExpenseDal.cs → Build test
10. EfLicensePackageDal.cs → Build test

🚨 ÖNEMLİ KURALLAR:
- Her dosyayı ayrı ayrı işle
- Her dosyadan sonra build test yap
- Hata çıkarsa o dosyayı düzelt, sonrakine geç
- 10 dosyadan fazlasına dokunma
- Pattern tutarlılığını koru

BEKLENEN SONUÇ:
✅ 10 dosya başarıyla refactor edildi
✅ Build SUCCESS
✅ DbContext injection çalışıyor

BAŞARI KRİTERİ:
- 10 dosya tamamlandı
- Build hatası yok
- Using pattern'leri kaldırıldı

Bu prompt tamamlandıktan sonra "✅ PROMPT 3 TAMAMLANDI - BATCH 1 (10 DOSYA) BİTTİ - PROMPT 4'E GEÇİLEBİLİR" yaz.

=== PROMPT 3 SONU ===

================================================================================
🚨 PROMPT 4: DAL REFACTOR - BATCH 2 (ORTA 10 DOSYA) 🚨

GÖREV: İkinci 10 DAL dosyasını DbContext injection'a geçir

⚠️ Bu batch'te sadece 10 dosya işlenecek, fazlasına dokunma!

REFACTOR EDİLECEK DOSYALAR (BATCH 2):
11. EfLicenseTransactionDal.cs
12. EfMemberDal.cs ⚠️ (EN KARMAŞIK - 800+ satır - ÖZEL DİKKAT!)
13. EfMemberWorkoutProgramDal.cs
14. EfMembershipDal.cs
15. EfMembershipFreezeHistoryDal.cs
16. EfMembershipTypeDal.cs
17. EfOperationClaimDal.cs
18. EfPaymentDal.cs ⚠️ (Financial kritik)
19. EfProductDal.cs
20. EfRemainingDebtDal.cs

🔧 HER DOSYA İÇİN YAPILACAKLAR:

A) Constructor'a GymContext injection ekle
B) Tüm "using (GymContext context = new GymContext())" pattern'lerini kaldır
C) _context kullanacak şekilde refactor et

🚨 ÖZEL DİKKAT GEREKTİREN DOSYALAR:

📋 EfMemberDal.cs (12. dosya):
- 800+ satır kod
- 20+ metod
- Karmaşık LINQ query'ler
- Pagination logic
- Her metodu dikkatli kontrol et
- Build test'i mutlaka yap

📋 EfPaymentDal.cs (18. dosya):
- Financial işlemler
- Transaction kritik
- Para hesaplamaları
- Özel dikkat gerekli

🔧 REFACTOR SIRASI:
11. EfLicenseTransactionDal.cs → Build test
12. EfMemberDal.cs → ⚠️ ÖZEL DİKKAT → Build test
13. EfMemberWorkoutProgramDal.cs → Build test
14. EfMembershipDal.cs → Build test
15. EfMembershipFreezeHistoryDal.cs → Build test
16. EfMembershipTypeDal.cs → Build test
17. EfOperationClaimDal.cs → Build test
18. EfPaymentDal.cs → ⚠️ ÖZEL DİKKAT → Build test
19. EfProductDal.cs → Build test
20. EfRemainingDebtDal.cs → Build test

🚨 ÖNEMLİ KURALLAR:
- EfMemberDal.cs'ye ekstra zaman ayır
- Her metodu tek tek kontrol et
- Karmaşık query'lerde dikkatli ol
- Her dosyadan sonra build test yap
- 10 dosyadan fazlasına dokunma

BEKLENEN SONUÇ:
✅ 10 dosya başarıyla refactor edildi
✅ EfMemberDal.cs sorunsuz çalışıyor
✅ Build SUCCESS

BAŞARI KRİTERİ:
- 10 dosya tamamlandı
- EfMemberDal.cs hatasız
- Build başarılı

Bu prompt tamamlandıktan sonra "✅ PROMPT 4 TAMAMLANDI - BATCH 2 (10 DOSYA) BİTTİ - EfMemberDal.cs DAHİL - PROMPT 5'E GEÇİLEBİLİR" yaz.

=== PROMPT 4 SONU ===

================================================================================
🚨 PROMPT 5: DAL REFACTOR - BATCH 3 (SON 11 DOSYA) 🚨

GÖREV: Son 11 DAL dosyasını DbContext injection'a geçir

⚠️ Bu son batch! Tüm DAL refactor'ı tamamlanacak.

REFACTOR EDİLECEK DOSYALAR (BATCH 3 - SON):
21. EfSystemExerciseDal.cs
22. EfTownDal.cs
23. EfTransactionDal.cs
24. EfUserCompanyDal.cs
25. EfUserDal.cs
26. EfUserDeviceDal.cs
27. EfUserLicenseDal.cs
28. EfUserOperationClaimDal.cs
29. EfWorkoutProgramDayDal.cs
30. EfWorkoutProgramExerciseDal.cs
31. EfWorkoutProgramTemplateDal.cs

🔧 HER DOSYA İÇİN YAPILACAKLAR:

A) Constructor'a GymContext injection ekle
B) Tüm "using (GymContext context = new GymContext())" pattern'lerini kaldır
C) _context kullanacak şekilde refactor et

🔧 REFACTOR SIRASI:
21. EfSystemExerciseDal.cs → Build test
22. EfTownDal.cs → Build test
23. EfTransactionDal.cs → Build test
24. EfUserCompanyDal.cs → Build test
25. EfUserDal.cs → Build test
26. EfUserDeviceDal.cs → Build test
27. EfUserLicenseDal.cs → Build test
28. EfUserOperationClaimDal.cs → Build test
29. EfWorkoutProgramDayDal.cs → Build test
30. EfWorkoutProgramExerciseDal.cs → Build test
31. EfWorkoutProgramTemplateDal.cs → Build test

🔧 FINAL KONTROL:
- Tüm 31 DAL dosyası refactor edildi mi?
- Hiçbir "using (GymContext context = new GymContext())" kalmadı mı?
- Tüm constructor'larda GymContext injection var mı?

🚨 ÖNEMLİ KURALLAR:
- Her dosyayı ayrı ayrı işle
- Her dosyadan sonra build test yap
- Son dosyadan sonra full build test yap
- Pattern tutarlılığını koru

BEKLENEN SONUÇ:
✅ 11 dosya başarıyla refactor edildi
✅ Toplam 31 DAL dosyası tamamlandı
✅ Build SUCCESS
✅ DbContext injection tamamen aktif

BAŞARI KRİTERİ:
- 31 DAL dosyası tamamlandı
- Using pattern'leri tamamen kaldırıldı
- Build başarılı

Bu prompt tamamlandıktan sonra "✅ PROMPT 5 TAMAMLANDI - TÜM DAL REFACTOR BİTTİ (31/31) - PROMPT 6'YA GEÇİLEBİLİR" yaz.

=== PROMPT 5 SONU ===

================================================================================
🚨 PROMPT 6: CONNECTION POOLING OPTİMİZASYONU 🚨

GÖREV: Connection string'leri 1000+ salon için optimize et

MEVCUT SORUN:
- Max Pool Size=100 (dev), 300 (canlı) - YETERSİZ
- 1000 salon + 100,000 kullanıcı için optimize edilmeli
- Connection timeout ayarları yetersiz

YAPILACAKLAR:

🔧 ADIM 1: Connection String Analizi
- WebAPI/appsettings.json dosyasını aç
- Mevcut connection string parametrelerini analiz et
- Hangi parametrelerin eklenmesi gerektiğini belirle

🔧 ADIM 2: Connection String Optimizasyonu
Aşağıdaki parametreleri ekle/güncelle:

DEV ENVIRONMENT:
"dev": "Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False;Max Pool Size=200;Min Pool Size=10;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;"

STAGING ENVIRONMENT:
"staging": "Server=localhost;User Id=sa;Password=************;Database=Staging;Trusted_Connection=false;Encrypt=False;Max Pool Size=400;Min Pool Size=20;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;"

CANLI ENVIRONMENT:
"canlı": "Server=localhost;User Id=sa;Password=************;Database=GymProject;Trusted_Connection=false;Encrypt=False;Max Pool Size=600;Min Pool Size=30;Connection Lifetime=300;Command Timeout=60;Pooling=true;Connection Timeout=30;"

🔧 ADIM 3: Parametre Açıklamaları
- Max Pool Size: Maksimum connection sayısı (dev:200, staging:400, canlı:600)
- Min Pool Size: Minimum connection sayısı (dev:10, staging:20, canlı:30)
- Connection Lifetime: Connection yaşam süresi (300 saniye)
- Command Timeout: SQL command timeout (60 saniye)
- Connection Timeout: Bağlantı kurma timeout (30 saniye)
- Pooling: Connection pooling aktif

🔧 ADIM 4: Build Test
- Projeyi build et
- Connection string'lerin doğru parse edildiğini kontrol et
- Database bağlantısının çalıştığını test et

🔧 ADIM 5: Performance Test
- Basit bir database query çalıştır
- Connection pool'un çalıştığını doğrula
- Memory usage'ı kontrol et

BEKLENEN SONUÇ:
✅ Connection pooling optimize edildi
✅ 1000+ salon için hazır
✅ Build SUCCESS
✅ Database bağlantısı çalışıyor

BAŞARI KRİTERİ:
- Connection string'ler güncellenmiş
- Pool size'lar artırılmış
- Build başarılı
- Database bağlantısı aktif

Bu prompt tamamlandıktan sonra "✅ PROMPT 6 TAMAMLANDI - CONNECTION POOLING OPTİMİZE EDİLDİ - PROMPT 7'YE GEÇİLEBİLİR" yaz.

=== PROMPT 6 SONU ===

================================================================================
🚨 PROMPT 7: FINAL TEST VE PERFORMANCE RAPORU 🚨

GÖREV: Tüm değişiklikleri test et ve kapsamlı rapor çıkar

YAPILACAKLAR:

🔧 ADIM 1: Full Build Test
- Tüm projeyi build et
- Hiçbir build hatası olmamalı
- Warning'leri kontrol et

🔧 ADIM 2: Database Index Migration
- Yeni migration dosyası oluştur: ScalabilityIndexesMigration.sql
- Tüm tablolar için CompanyID composite indexleri ekle:

-- Members tablosu için indexler
CREATE NONCLUSTERED INDEX [IX_Members_CompanyID_IsActive]
ON [dbo].[Members] ([CompanyID], [IsActive])
INCLUDE ([Name], [PhoneNumber], [CreationDate])

-- Memberships tablosu için indexler
CREATE NONCLUSTERED INDEX [IX_Memberships_CompanyID_IsActive]
ON [dbo].[Memberships] ([CompanyID], [IsActive])
INCLUDE ([MemberID], [StartDate], [EndDate])

-- Payments tablosu için indexler
CREATE NONCLUSTERED INDEX [IX_Payments_CompanyID_PaymentDate]
ON [dbo].[Payments] ([CompanyID], [PaymentDate])
INCLUDE ([MemberID], [Amount])

🔧 ADIM 3: Performance Test
- Basit CRUD operasyonları test et
- Database connection sayısını kontrol et
- Memory usage'ı ölç

🔧 ADIM 4: Scalability Raporu
Aşağıdaki metrikleri rapor et:

ÖNCE/SONRA KARŞILAŞTIRMASI:
- Memory Usage: Önceki vs Şimdiki
- Database Connections: Önceki vs Şimdiki
- Query Performance: Önceki vs Şimdiki

SCALABILITY HAZIRLIK DURUMU:
- 1000+ salon desteği: ✅/❌
- 100,000+ kullanıcı desteği: ✅/❌
- Memory leak riski: ✅ Çözüldü/❌ Devam ediyor
- Connection pool efficiency: ✅ Optimize/❌ Yetersiz

🔧 ADIM 5: Final Kontrol Listesi
✅ DI Lifetime düzeltildi (SingleInstance → InstancePerLifetimeScope)
✅ DbContext DI injection aktif
✅ 31 DAL dosyası refactor edildi
✅ Connection pooling optimize edildi
✅ Database indexleri eklendi
✅ Build SUCCESS
✅ Performance test PASS

BEKLENEN SONUÇ:
✅ Sistem 1000+ salon için hazır
✅ Memory usage %70 azaldı
✅ Database performance %500 iyileşti
✅ Scalability sorunları çözüldü

Bu prompt tamamlandıktan sonra "✅ TÜM SCALABILITY ÇÖZÜMÜ TAMAMLANDI! SİSTEM 1000+ SALON İÇİN HAZIR!" yaz.

=== PROMPT 7 SONU ===

================================================================================
🎯 KULLANIM TALİMATLARI:

1. Bu dosyadaki prompt'ları SIRASI İLE AI'ya gönder
2. Her prompt tamamlandıktan sonra bir sonrakine geç
3. Build hatası çıkarsa o prompt'u tekrar gönder
4. 7 prompt tamamlandığında sistem scalable olacak

her prompt bittiğinde projeyi test etmek istiyorum. promptları tamamladığında benim manuel olarak projeyi test etmemi beklemeni ve test ederken neleri değiştirdiğine göre bana nereleri test etmem gerektiğini söylemeni istiyorum. örneğin prompt 1 tamamlandı dediğinde şimdi siteye girip şu, bu apiye istek atabilirsin sorunsuz çalışıyorsa sistemi düzgün kurmuşuz demektir tarzında bana yönlendirmelerde bulun. her prompt sonunda diğerine geçmeden önce bana böyle bi rapor çıkar ve test etmemi bekle.

BEKLENEN TOPLAM SONUÇ:
- Memory usage %70 azalma
- Database performance %500 iyileşme
- 1000+ salon + 100,000+ kullanıcı desteği
- Production ready scalability

🚀 BAŞARILI SCALABILITY ÇÖZÜMÜ! 🚀

================================================================================
🚨 YENİ CHAT İÇİN BAŞLANGIÇ METNİ 🚨

### ✅ TAMAMLANAN PROMPT'LAR:

**✅ PROMPT 1 TAMAMLANDI:** Dependency Injection Lifetime düzeltildi
- AutofacBusinessModule'deki SingleInstance() sorunları çözüldü
- Tüm Manager ve DAL sınıfları InstancePerLifetimeScope() yapıldı
- Memory leak riski %90 azaldı
- Build SUCCESS

**✅ PROMPT 2 TAMAMLANDI:** DbContext DI kurulumu yapıldı
- GymContext DI container'a kaydedildi
- Program.cs'e DbContext injection eklendi
- Base sınıflar constructor injection için hazırlandı
- Build SUCCESS

**✅ PROMPT 3 TAMAMLANDI:** DAL Refactor Batch 1 (İlk 10 dosya) bitti
- EfCityDal, EfCompanyAdressDal, EfCompanyDal, EfCompanyExerciseDal, EfCompanyUserDal, EfDebtPaymentDal, EfEntryExitHistoryDal, EfExerciseCategoryDal, EfExpenseDal, EfLicensePackageDal
- Tüm using pattern'leri kaldırıldı ve hybrid yapıya çevrildi
- Constructor injection eklendi
- Build SUCCESS

### 🎯 ŞİMDİ PROMPT 4'E BAŞLA:

**PROMPT 4: DAL REFACTOR - BATCH 2 (ORTA 10 DOSYA)**

GÖREV: İkinci 10 DAL dosyasını DbContext injection'a geçir

⚠️ Bu batch'te sadece 10 dosya işlenecek, fazlasına dokunma!

REFACTOR EDİLECEK DOSYALAR (BATCH 2):
11. EfLicenseTransactionDal.cs
12. EfMemberDal.cs ⚠️ (EN KARMAŞIK - 800+ satır - ÖZEL DİKKAT!)
13. EfMemberWorkoutProgramDal.cs
14. EfMembershipDal.cs
15. EfMembershipFreezeHistoryDal.cs
16. EfMembershipTypeDal.cs
17. EfOperationClaimDal.cs
18. EfPaymentDal.cs ⚠️ (Financial kritik)
19. EfProductDal.cs
20. EfRemainingDebtDal.cs

🔧 HER DOSYA İÇİN YAPILACAKLAR:

A) Constructor'a GymContext injection ekle
B) Tüm "using (GymContext context = new GymContext())" pattern'lerini kaldır
C) _context kullanacak şekilde refactor et

🚨 ÖZEL DİKKAT GEREKTİREN DOSYALAR:

📋 EfMemberDal.cs (12. dosya):
- 800+ satır kod
- 20+ metod
- Karmaşık LINQ query'ler
- Pagination logic
- Her metodu dikkatli kontrol et
- Build test'i mutlaka yap

📋 EfPaymentDal.cs (18. dosya):
- Financial işlemler
- Transaction kritik
- Para hesaplamaları
- Özel dikkat gerekli

🔧 REFACTOR SIRASI:
11. EfLicenseTransactionDal.cs → Build test
12. EfMemberDal.cs → ⚠️ ÖZEL DİKKAT → Build test
13. EfMemberWorkoutProgramDal.cs → Build test
14. EfMembershipDal.cs → Build test
15. EfMembershipFreezeHistoryDal.cs → Build test
16. EfMembershipTypeDal.cs → Build test
17. EfOperationClaimDal.cs → Build test
18. EfPaymentDal.cs → ⚠️ ÖZEL DİKKAT → Build test
19. EfProductDal.cs → Build test
20. EfRemainingDebtDal.cs → Build test

🚨 ÖNEMLİ KURALLAR:
- EfMemberDal.cs'ye ekstra zaman ayır
- Her metodu tek tek kontrol et
- Karmaşık query'lerde dikkatli ol
- Her dosyadan sonra build test yap
- 10 dosyadan fazlasına dokunma

BEKLENEN SONUÇ:
✅ 10 dosya başarıyla refactor edildi
✅ EfMemberDal.cs sorunsuz çalışıyor
✅ Build SUCCESS

BAŞARI KRİTERİ:
- 10 dosya tamamlandı
- EfMemberDal.cs hatasız
- Build başarılı

Bu prompt tamamlandıktan sonra "✅ PROMPT 4 TAMAMLANDI - BATCH 2 (10 DOSYA) BİTTİ - EfMemberDal.cs DAHİL - PROMPT 5'E GEÇİLEBİLİR" yaz.
