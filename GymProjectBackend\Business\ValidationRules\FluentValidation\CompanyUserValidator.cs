﻿using Core.Entities.Concrete;
using DataAccess.Concrete.EntityFramework;
using FluentValidation;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class CompanyUserValidator : AbstractValidator<CompanyUser>
    {
        private readonly GymContext _context;

        // Constructor injection (Scalability için)
        public CompanyUserValidator(GymContext context)
        {
            _context = context;

            RuleFor(p => p.Name).NotEmpty().WithMessage("İsim kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).NotEmpty().WithMessage("Telefon kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).Must((user, phone) => BeUniquePhoneNumber(user))
                .WithMessage("Bu telefon numarası sistemde zaten kayıtlı.");
            RuleFor(p => p.PhoneNumber).Length(11).WithMessage("Telefon numarasını kontrol ediniz.");
            RuleFor(p => p.PhoneNumber).Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır");
            RuleFor(x => x.Email).EmailAddress().WithMessage("E Posta adresini doğru giriniz.");
            RuleFor(x => x).Must(BeUniqueEmail).WithMessage("Bu e-posta adresi sistemde zaten kayıtlı.");
            RuleFor(p => p.CityID).NotEmpty().WithMessage("İl kısmı boş bırakılamaz.");
            RuleFor(p => p.TownID).NotEmpty().WithMessage("İlçe kısmı boş bırakılamaz.");
        }

        private bool BeUniqueEmail(CompanyUser user)
        {
            // DI kullanılıyor - Scalability optimized
            if (user.CompanyUserID != 0)
            {
                return !_context.CompanyUsers.Any(u =>
                    u.Email == user.Email &&
                    u.CompanyUserID != user.CompanyUserID &&
                    u.IsActive == true);
            }
            else
            {
                return !_context.CompanyUsers.Any(u =>
                    u.Email == user.Email &&
                    u.IsActive == true);
            }
        }

        private bool BeUniquePhoneNumber(CompanyUser user)
        {
            // DI kullanılıyor - Scalability optimized
            if (user.CompanyUserID != 0)
            {
                return !_context.CompanyUsers.Any(u =>
                    u.PhoneNumber == user.PhoneNumber &&
                    u.CompanyUserID != user.CompanyUserID &&
                    u.IsActive == true);
            }
            else
            {
                return !_context.CompanyUsers.Any(u =>
                    u.PhoneNumber == user.PhoneNumber &&
                    u.IsActive == true);
            }
        }

        private bool StartsWithZero(string arg)
        {
            return arg.StartsWith("0");
        }
    }
}