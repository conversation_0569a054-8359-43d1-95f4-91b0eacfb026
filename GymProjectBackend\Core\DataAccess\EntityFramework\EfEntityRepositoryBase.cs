﻿using Core.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace Core.DataAccess.EntityFramework
{
    public class EfEntityRepositoryBase<TEntity, TContext> : IEntityRepository<TEntity>
        where TEntity : class, IEntity, new()
        where TContext : DbContext, new()
    {
        protected readonly TContext _context;

        // Constructor injection (Scalability için) - Artık zorunlu
        public EfEntityRepositoryBase(TContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public void Add(TEntity entity)
        {
            // DI kullanılıyor - Scalability optimized
            var addedEntity = _context.Entry(entity);
            addedEntity.State = EntityState.Added;
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                addedEntity.Property("CreationDate").CurrentValue = DateTime.Now;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                addedEntity.Property("DeletedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                addedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (addedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                addedEntity.Property("IsActive").CurrentValue = true;
            }
            _context.SaveChanges();
        }

        public void Delete(object id)
        {
            // DI kullanılıyor - Scalability optimized
            TEntity entity = _context.Set<TEntity>().Find(id);
            if (entity == null) return;

            var deletedEntity = _context.Entry(entity);
            deletedEntity.State = EntityState.Modified;

            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                deletedEntity.Property("CreationDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                deletedEntity.Property("DeletedDate").CurrentValue = DateTime.Now;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                deletedEntity.Property("UpdatedDate").IsModified = false;
            }
            if (deletedEntity.Properties.Any(p => p.Metadata.Name == "IsActive"))
            {
                deletedEntity.Property("IsActive").CurrentValue = false;
            }

            _context.SaveChanges();
        }

        public void HardDelete(object id)
        {
            // DI kullanılıyor - Scalability optimized
            TEntity entity = _context.Set<TEntity>().Find(id);
            if (entity == null) return;

            var deletedEntity = _context.Entry(entity);
            deletedEntity.State = EntityState.Deleted;

            _context.SaveChanges();
        }

        public TEntity Get(Expression<Func<TEntity, bool>> filter)
        {
            // DI kullanılıyor - Scalability optimized
            return _context.Set<TEntity>().SingleOrDefault(filter);
        }

        public List<TEntity> GetAll(Expression<Func<TEntity, bool>> filter = null)
        {
            // DI kullanılıyor - Scalability optimized
            return filter == null
                ? _context.Set<TEntity>().ToList()
                : _context.Set<TEntity>().Where(filter).ToList();
        }

        public void Update(TEntity entity)
        {
            // DI kullanılıyor - Scalability optimized
            var updatedEntity = _context.Entry(entity);
            updatedEntity.State = EntityState.Modified;
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "CreationDate"))
            {
                updatedEntity.Property("CreationDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "DeletedDate"))
            {
                updatedEntity.Property("DeletedDate").IsModified = false;
            }
            if (updatedEntity.Properties.Any(p => p.Metadata.Name == "UpdatedDate"))
            {
                updatedEntity.Property("UpdatedDate").CurrentValue = DateTime.Now;
            }
            _context.SaveChanges();
        }
    }
}
